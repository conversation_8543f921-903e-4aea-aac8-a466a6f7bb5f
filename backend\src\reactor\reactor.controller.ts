import { Consts, Common } from "@commune/api";
import { FileInterceptor } from "@nestjs/platform-express";
import {
    Controller,
    Delete,
    FileTypeValidator,
    MaxFileSizeValidator,
    NotFoundException,
    Param,
    ParseFilePipe,
    Post,
    Put,
    UploadedFile,
    UseGuards,
    UseInterceptors,
} from "@nestjs/common";
import { ZodPipe } from "src/zod";
import { getServer } from "src/acrpc";
import { CurrentUser } from "src/auth/types";
import { HttpCurrentUser } from "src/auth/http/current-user.decorator";
import { HttpSessionAuthGuard } from "src/auth/http/session-auth.guard";
import { ReactorHubService } from "./reactor-hub.service";
import { ReactorPostService } from "./reactor-post.service";
import { ReactorLensService } from "./lens/reactor-lens.service";
import { ReactorCommentService } from "./reactor-comment.service";
import { ReactorCommunityService } from "./reactor-community.service";

@Controller("reactor")
@UseGuards(HttpSessionAuthGuard)
export class ReactorController {
    constructor(
        private readonly reactorPostService: ReactorPostService,
        private readonly reactorCommentService: ReactorCommentService,
        private readonly reactorLensService: ReactorLensService,
        private readonly reactorHubService: ReactorHubService,
        private readonly reactorCommunityService: ReactorCommunityService,
    ) {
        const acrpcServer = getServer();

        acrpcServer.register({
            reactor: {
                post: {
                    list: {
                        get: (input, metadata) =>
                            this.reactorPostService.getPosts(
                                input,
                                metadata.user,
                            ),
                    },
                    post: (input, metadata) =>
                        this.reactorPostService.createPost(
                            input,
                            metadata.user,
                        ),
                    patch: (input, metadata) =>
                        this.reactorPostService.updatePost(
                            input,
                            metadata.user,
                        ),
                    delete: (input, metadata) =>
                        this.reactorPostService.deletePost(
                            input,
                            metadata.user,
                        ),
                    rating: {
                        post: (input, metadata) =>
                            this.reactorPostService.updatePostRating(
                                input,
                                metadata.user,
                            ),
                    },
                    usefulness: {
                        post: (input, metadata) =>
                            this.reactorPostService.updatePostUsefulness(
                                input,
                                metadata.user,
                            ),
                    },
                },
                comment: {
                    list: {
                        get: (input, metadata) =>
                            this.reactorCommentService.getComments(
                                input,
                                metadata.user,
                            ),
                    },
                    post: (input, metadata) =>
                        this.reactorCommentService.createComment(
                            input,
                            metadata.user,
                        ),
                    patch: (input, metadata) =>
                        this.reactorCommentService.updateComment(
                            input,
                            metadata.user,
                        ),
                    delete: (input, metadata) =>
                        this.reactorCommentService.deleteComment(
                            input,
                            metadata.user,
                        ),
                    rating: {
                        post: (input, metadata) =>
                            this.reactorCommentService.updateCommentRating(
                                input,
                                metadata.user,
                            ),
                    },
                    anonimify: {
                        post: (input, metadata) =>
                            this.reactorCommentService.anonimifyComment(
                                input,
                                metadata.user,
                            ),
                    },
                },
                lens: {
                    list: {
                        get: (_, metadata) =>
                            this.reactorLensService.getLenses(metadata.user),
                    },
                    post: (input, metadata) =>
                        this.reactorLensService.createLens(
                            input,
                            metadata.user,
                        ),
                    patch: (input, metadata) =>
                        this.reactorLensService.updateLens(
                            input,
                            metadata.user,
                        ),
                    delete: (input, metadata) =>
                        this.reactorLensService.deleteLens(
                            input,
                            metadata.user,
                        ),
                },
                hub: {
                    list: {
                        get: async (input, metadata) => {
                            const hubs = await this.reactorHubService.getHubs(
                                input,
                                metadata.user,
                            );

                            return hubs.map((hub) => ({
                                ...hub,
                                headUser: {
                                    ...hub.headUser,
                                    image: hub.headUser.image?.url ?? null,
                                },
                                image: hub.image?.url ?? null,
                            }));
                        },
                    },
                    post: (input, metadata) =>
                        this.reactorHubService.createHub(input, metadata.user),
                    patch: (input, metadata) =>
                        this.reactorHubService.updateHub(input, metadata.user),
                },
                community: {
                    list: {
                        get: async (input, metadata) => {
                            const communities =
                                await this.reactorCommunityService.getCommunities(
                                    input,
                                    metadata.user,
                                );

                            return communities.map((community) => ({
                                ...community,
                                hub: community.hub
                                    ? {
                                          ...community.hub,
                                          image:
                                              community.hub.image?.url ?? null,
                                      }
                                    : null,
                                headUser: {
                                    ...community.headUser,
                                    image:
                                        community.headUser.image?.url ?? null,
                                },
                                image: community.image?.url ?? null,
                            }));
                        },
                    },
                    post: (input, metadata) =>
                        this.reactorCommunityService.createCommunity(
                            input,
                            metadata.user,
                        ),
                    patch: (input, metadata) =>
                        this.reactorCommunityService.updateCommunity(
                            input,
                            metadata.user,
                        ),
                },
            },
        });
    }

    // posts

    @Post("__mock_post__")
    async createMockPost(@HttpCurrentUser() user: CurrentUser) {
        if (!user.isAdmin) {
            throw new NotFoundException();
        }

        return await this.reactorPostService.createMockPost();
    }

    // hubs

    @Put("hub/:id/image")
    @UseInterceptors(FileInterceptor("image"))
    async updateHubImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,

        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({
                        maxSize: Consts.MAX_IMAGE_FILE_SIZE,
                    }),
                    new FileTypeValidator({
                        fileType: Consts.ALLOWED_IMAGE_FILE_TYPES.join("|"),
                    }),
                ],
            }),
        )
        file: Express.Multer.File,
    ) {
        await this.reactorHubService.updateHubImage(id, file, user);
    }

    @Delete("hub/:id/image")
    async deleteHubImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.reactorHubService.deleteHubImage(id, user);
    }

    // communities

    @Put("community/:id/image")
    @UseInterceptors(FileInterceptor("image"))
    async updateCommunityImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,

        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({
                        maxSize: Consts.MAX_IMAGE_FILE_SIZE,
                    }),
                    new FileTypeValidator({
                        fileType: Consts.ALLOWED_IMAGE_FILE_TYPES.join("|"),
                    }),
                ],
            }),
        )
        file: Express.Multer.File,
    ) {
        await this.reactorCommunityService.updateCommunityImage(id, file, user);
    }

    @Delete("community/:id/image")
    async deleteCommunityImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.reactorCommunityService.deleteCommunityImage(id, user);
    }
}
