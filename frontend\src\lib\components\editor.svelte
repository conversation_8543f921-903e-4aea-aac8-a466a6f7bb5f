<script lang="ts">
  import type { RawEditorOptions } from "tinymce";

  import TinyMceEditor from "@tinymce/tinymce-svelte";

  export type Props = {
    content: string;
  };

  let { content = $bindable() }: Props = $props();

  const editorConfig: RawEditorOptions = {
    // plugins: ["lists", "image"],
  };
</script>

<h3>Sample Editor</h3>
<TinyMceEditor
  licenseKey="gpl"
  scriptSrc="/tinymce/tinymce.min.js"
  conf={editorConfig}
  bind:value={content}
/>
