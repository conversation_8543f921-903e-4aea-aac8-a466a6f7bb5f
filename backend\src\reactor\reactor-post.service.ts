import { Reactor } from "@commune/api";
import { ReactorRatingType } from "@prisma/client";
import { Sql } from "@prisma/client/runtime/library";
import { faker, fakerEN, fakerRU } from "@faker-js/faker";
import {
    ForbiddenException,
    Injectable,
    Logger,
    NotFoundException,
} from "@nestjs/common";
import { getError } from "src/common/errors";
import { CurrentUser } from "src/auth/types";
import { toPrismaLocalizations } from "src/utils";
import { PrismaService } from "src/prisma/prisma.service";
import { RatingService } from "src/rating/rating.service";

function mockId(index: number) {
    return "0".repeat(20) + index.toString();
}

function range(from: number, to: number) {
    return new Array(to - from + 1).fill(0).map((_, i) => from + i);
}

function randomRange(min: number, max: number) {
    return range(min, faker.number.int({ min, max }));
}

@Injectable()
export class ReactorPostService {
    private readonly logger = new Logger(ReactorPostService.name);

    constructor(
        private readonly prisma: PrismaService,
        private readonly ratingService: RatingService,
    ) {}

    async createPost(dto: Reactor.CreatePostInput, user: CurrentUser) {
        const post = await this.prisma.reactorPost.create({
            data: {
                hubId: dto.hubId,
                communityId: dto.communityId,

                authorId: user.id,

                title: {
                    create: toPrismaLocalizations(dto.title, "title"),
                },

                body: {
                    create: toPrismaLocalizations(dto.body, "body"),
                },

                tags: {
                    connect: dto.tagIds.map((tag) => ({ id: tag })),
                },
            },
        });

        return { id: post.id };
    }

    async updatePost(input: Reactor.UpdatePostInput, user: CurrentUser) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id: input.id },
        });

        if (!post) {
            throw new NotFoundException(...getError("post_not_found"));
        }

        if (user.role !== "admin" && user.id !== post.authorId) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_author"),
            );
        }

        await this.prisma.reactorPost.update({
            where: { id: input.id },
            data: {
                title: input.title && {
                    deleteMany: {},
                    create: toPrismaLocalizations(input.title, "title"),
                },
                body: input.body && {
                    deleteMany: {},
                    create: toPrismaLocalizations(input.body, "body"),
                },
                tags: input.tagIds && {
                    set: input.tagIds.map((tag) => ({ id: tag })),
                },
            },
        });

        return true;
    }

    async updatePostRating(
        input: Reactor.UpdatePostRatingInput,
        user: CurrentUser,
    ) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id: input.id },
        });

        if (!post) {
            throw new NotFoundException(...getError("post_not_found"));
        }

        const existingRating = await this.prisma.reactorRating.findFirst({
            where: {
                userId: user.id,
                entityType: "post",
                entityId: post.id,
            },
        });

        let newStatus: ReactorRatingType | null = null;

        if (existingRating) {
            if (existingRating.type === input.type) {
                await this.prisma.$transaction(async (trx) => {
                    await trx.reactorRating.delete({
                        where: { id: existingRating.id },
                    });

                    await this.ratingService.deleteRelativeUserRating(
                        {
                            sourceUserId: user.id,
                            targetUserId: post.authorId,
                            entityType: "post",
                            entityId: post.id,
                        },
                        trx,
                    );
                });
            } else {
                await this.prisma.$transaction(async (trx) => {
                    await trx.reactorRating.update({
                        where: { id: existingRating.id },
                        data: {
                            type: input.type,
                        },
                    });

                    await this.ratingService.upsertRelativeUserRating(
                        {
                            sourceUserId: user.id,
                            targetUserId: post.authorId,
                            entityType: "post",
                            entityId: post.id,
                            value: input.type === "like" ? 1 : -1,
                        },
                        trx,
                    );
                });

                newStatus = input.type;
            }
        } else {
            await this.prisma.$transaction(async (trx) => {
                await trx.reactorRating.create({
                    data: {
                        userId: user.id,
                        entityType: "post",
                        entityId: post.id,
                        type: input.type,
                    },
                });

                await this.ratingService.upsertRelativeUserRating(
                    {
                        sourceUserId: user.id,
                        targetUserId: post.authorId,
                        entityType: "post",
                        entityId: post.id,
                        value: input.type === "like" ? 1 : -1,
                    },
                    trx,
                );
            });

            newStatus = input.type;
        }

        const [likes, dislikes] = await Promise.all([
            this.prisma.reactorRating.count({
                where: {
                    entityType: "post",
                    entityId: post.id,
                    type: "like",
                },
            }),
            this.prisma.reactorRating.count({
                where: {
                    entityType: "post",
                    entityId: post.id,
                    type: "dislike",
                },
            }),
        ]);

        return {
            likes,
            dislikes,
            status: newStatus,
        };
    }

    async updatePostUsefulness(
        input: Reactor.UpdatePostUsefulnessInput,
        user: CurrentUser,
    ) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id: input.id },
        });

        if (!post) {
            throw new NotFoundException(...getError("post_not_found"));
        }

        if (input.value === null) {
            await this.prisma.reactorUsefulness.delete({
                where: {
                    entityType_entityId_userId: {
                        entityType: "post",
                        entityId: post.id,
                        userId: user.id,
                    },
                },
            });
        } else {
            await this.prisma.reactorUsefulness.upsert({
                where: {
                    entityType_entityId_userId: {
                        entityType: "post",
                        entityId: post.id,
                        userId: user.id,
                    },
                },
                create: {
                    entityId: post.id,
                    entityType: "post",
                    userId: user.id,
                    value: input.value,
                },
                update: {
                    value: input.value,
                },
            });
        }

        const [count, totalValue] = await Promise.all([
            this.prisma.reactorUsefulness.count({
                where: {
                    entityType: "post",
                    entityId: post.id,
                },
            }),
            this.prisma.reactorUsefulness.aggregate({
                where: {
                    entityType: "post",
                    entityId: post.id,
                },
                _avg: {
                    value: true,
                },
            }),
        ]);

        return {
            count,
            totalValue: totalValue._avg.value,
            value: input.value,
        };
    }

    async deletePost(input: Reactor.DeletePostInput, user: CurrentUser) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id: input.id },
        });

        if (!post) {
            throw new NotFoundException(...getError("post_not_found"));
        }

        if (user.role !== "admin" && user.id !== post.authorId) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_author"),
            );
        }

        await this.prisma.reactorPost.update({
            where: { id: input.id },
            data: {
                deleteReason: input.reason,
                deletedAt: new Date(),
            },
        });

        return true;
    }

    async getPost(id: string, user: CurrentUser) {
        const [post] = await this.getPosts(
            {
                id,
                lensId: null,
                pagination: { page: 1, size: 1 },
            },
            user,
        );

        return post;
    }

    async getPosts(data: Reactor.GetPostsInput, user: CurrentUser) {
        let lensSql: string | null = null;

        if (data.lensId) {
            const lens = await this.prisma.reactorLens.findUnique({
                where: { id: data.lensId },
            });

            if (!lens) {
                throw new NotFoundException(...getError("lens_not_found"));
            }

            console.dir({ lens }, { depth: null });

            lensSql = lens.sql;
        }

        if (true) {
            // lensSql = "hub_id IN ('oTgqd4iLcWKXLlUkOlRsU')";
            // lensSql = "(hub_id NOT IN ('oTgqd4iLcWKXLlUkOlRsU', 'wNKuCEJO5g3ZfV0oWk51L') AND community_id NOT IN ('3-9iEDWNMAQSyZFPzZiQe'))";
            // lensSql = "EXISTS (SELECT 1 FROM UNNEST(body_values) AS b WHERE b ILIKE '%рес%')";
            // lensSql = "age < 16500";
        }

        const wheres = [];

        if (data.id) {
            wheres.push(`(posts.id = '${data.id}')`);
        }

        if (!user.isAdmin) {
            wheres.push("(posts.deleted_at IS NULL)");
        }

        const posts = await this.prisma.$queryRaw<Reactor.GetPostsOutput>`
            SELECT
                id,

                hub,
                community,

                author,

                rating,
                usefulness,

                title,
                body,

                tags,

                created_at AS "createdAt",
                updated_at AS "updatedAt"

                ${user.isAdmin ? new Sql([`, deleted_at AS "deletedAt"`], []) : new Sql([""], [])}
            FROM (
                SELECT
                    posts.id,

                    posts.hub_id,
                    CASE WHEN hub_data.id IS NOT NULL THEN to_jsonb(hub_data) END AS hub,

                    posts.community_id,
                    CASE WHEN community_data.id IS NOT NULL THEN to_jsonb(community_data) END AS community,

                    posts.author_id,
                    to_jsonb(author_data) AS author,

                    post_rating_data.likes - post_rating_data.dislikes AS total_rating,
                    JSONB_BUILD_OBJECT(
                        'likes', post_rating_data.likes,
                        'dislikes', post_rating_data.dislikes,
                        'status', post_rating_status_data.status
                    ) AS rating,

                    post_usefulness_total_data.value AS average_usefulness,
                    JSONB_BUILD_OBJECT(
                        'value', post_usefulness_status_data.value,
                        'count', post_usefulness_total_data.count,
                        'totalValue', post_usefulness_total_data.value
                    ) AS usefulness,

                    post_title_data.title,
                    post_body_data.body,

                    tag_data.tag_ids,
                    tag_data.tags,

                    EXTRACT(EPOCH FROM (NOW() - posts.created_at))::INT AS age,

                    posts.created_at,
                    posts.updated_at,
                    posts.deleted_at
                FROM reactor_posts posts

                LEFT JOIN LATERAL (
                    SELECT
                        posts.community_id AS id,
                        reactor_communities.hub_id as hub_id,
                        JSONB_AGG(
                            JSONB_BUILD_OBJECT(
                                'locale', l.locale,
                                'value',  l.value
                            )
                        ) AS name,
                        MAX(images.url) AS image
                    FROM reactor_communities
                    LEFT JOIN _reactor_community_name ON _reactor_community_name."B" = reactor_communities.id
                    LEFT JOIN localizations l ON l.id = _reactor_community_name."A"
                    LEFT JOIN images ON images.id = reactor_communities.image_id
                    WHERE reactor_communities.id = posts.community_id
                    GROUP BY reactor_communities.id
                ) AS community_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        reactor_hubs.id,
                        JSONB_AGG(
                            JSONB_BUILD_OBJECT(
                                'locale', l.locale,
                                'value',  l.value
                            )
                        ) AS name,
                        MAX(images.url) AS image
                    FROM reactor_hubs
                    LEFT JOIN _reactor_hub_name ON _reactor_hub_name."B" = reactor_hubs.id
                    LEFT JOIN localizations l ON l.id = _reactor_hub_name."A"
                    LEFT JOIN images ON images.id = reactor_hubs.image_id
                    WHERE reactor_hubs.id = COALESCE(community_data.hub_id, posts.hub_id)
                    GROUP BY reactor_hubs.id
                ) AS hub_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        users.id,
                        users.email,
                        JSONB_AGG(
                            JSONB_BUILD_OBJECT(
                                'locale', l.locale,
                                'value',  l.value
                            )
                        ) AS name,
                        MAX(images.url) AS image
                    FROM users
                    LEFT JOIN _user_name ON _user_name."B" = users.id
                    LEFT JOIN LATERAL (
                        SELECT id, url
                        FROM images
                        WHERE images.id = users.image_id
                        ORDER BY created_at DESC
                        LIMIT 1
                    ) AS images ON true
                    LEFT JOIN localizations l ON l.id = _user_name."A"
                    WHERE users.id = posts.author_id
                    GROUP BY users.id
                ) AS author_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        COALESCE(ARRAY_AGG(_reactor_post_tags."B"), '{}') AS tag_ids,
                        COALESCE(JSONB_AGG(to_jsonb(tag_names_data)), '[]') AS tags
                    FROM _reactor_post_tags
                    LEFT JOIN LATERAL (
                        SELECT
                            _reactor_post_tags."B" AS id,
                            JSONB_AGG(
                                JSONB_BUILD_OBJECT(
                                    'locale', l.locale,
                                    'value',  l.value
                                )
                            ) AS name
                        FROM _tag_name
                        LEFT JOIN localizations l ON l.id = _tag_name."A"
                        WHERE _tag_name."B" = _reactor_post_tags."B"
                        GROUP BY _reactor_post_tags."B"
                    ) AS tag_names_data ON true
                    WHERE _reactor_post_tags."A" = posts.id
                ) AS tag_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        JSONB_AGG(
                            JSONB_BUILD_OBJECT(
                                'locale', post_title.locale,
                                'value', post_title.value
                            )
                        ) AS title
                    FROM _reactor_post_title
                    LEFT JOIN localizations post_title
                        ON post_title.id = _reactor_post_title."A"
                    WHERE _reactor_post_title."B" = posts.id
                ) AS post_title_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        JSONB_AGG(
                            JSONB_BUILD_OBJECT(
                                'locale', post_body.locale,
                                'value', post_body.value
                            )
                        ) AS body
                    FROM _reactor_post_body
                    LEFT JOIN localizations post_body
                        ON post_body.id = _reactor_post_body."A"
                    WHERE _reactor_post_body."B" = posts.id
                ) AS post_body_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        COUNT(CASE WHEN rating.type = 'like' THEN 1 END)::INT AS likes,
                        COUNT(CASE WHEN rating.type = 'dislike' THEN 1 END)::INT AS dislikes
                    FROM reactor_ratings rating
                    WHERE
                        rating.entity_id = posts.id
                        AND rating.entity_type = 'post'

                ) AS post_rating_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        rating.type AS status
                    FROM reactor_ratings rating
                    WHERE
                        rating.entity_id = posts.id
                        AND rating.entity_type = 'post'
                        AND rating.user_id = ${user.id}
                ) AS post_rating_status_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        usefulness.value AS value
                    FROM reactor_usefulnesses usefulness
                    WHERE
                        usefulness.entity_id = posts.id
                        AND usefulness.entity_type = 'post'
                        AND usefulness.user_id = ${user.id}
                ) AS post_usefulness_status_data ON true

                LEFT JOIN LATERAL (
                    SELECT
                        AVG(usefulness.value)::FLOAT AS value,
                        COUNT(usefulness.id)::INT AS count
                    FROM reactor_usefulnesses usefulness
                    WHERE
                        usefulness.entity_id = posts.id
                        AND usefulness.entity_type = 'post'
                ) AS post_usefulness_total_data ON true

                ${
                    wheres.length
                        ? new Sql([`WHERE ${wheres.join(" AND ")}`], [])
                        : new Sql([""], [])
                }
            ) posts

            ${lensSql ? new Sql([`WHERE ${lensSql}`], []) : new Sql([""], [])}

            ORDER BY created_at DESC

            LIMIT ${data.pagination.size}
            OFFSET ${(data.pagination.page - 1) * data.pagination.size}
        `;

        return posts;
    }

    async createMockPost() {
        await this.prisma.user.createMany({
            data: range(1, 20).map((_, i) => ({
                id: mockId(i),
                email: fakerEN.internet.email(),
                role: "user",
            })),
            skipDuplicates: true,
        });

        const tags = await Promise.all(
            randomRange(2, 5).map(() =>
                this.prisma.tag.create({
                    data: {
                        name: {
                            create: toPrismaLocalizations(
                                [
                                    {
                                        locale: "en",
                                        value: fakerEN.word.sample(),
                                    },
                                    {
                                        locale: "ru",
                                        value: fakerRU.word.sample(),
                                    },
                                ],
                                "name",
                            ),
                        },
                    },
                }),
            ),
        );

        const hub = await this.prisma.reactorHub.create({
            data: {
                headUserId: mockId(0),
                name: {
                    create: toPrismaLocalizations(
                        [
                            {
                                locale: "en",
                                value: fakerEN.word.words(),
                            },
                            {
                                locale: "ru",
                                value: fakerRU.word.words(),
                            },
                        ],
                        "name",
                    ),
                },
                description: {
                    create: toPrismaLocalizations(
                        [
                            {
                                locale: "en",
                                value: fakerEN.lorem.words(50),
                            },
                            {
                                locale: "ru",
                                value: fakerRU.lorem.words(50),
                            },
                        ],
                        "description",
                    ),
                },
            },
        });

        const community = await this.prisma.reactorCommunity.create({
            data: {
                headUserId: mockId(0),
                hubId: hub.id,
                name: {
                    create: toPrismaLocalizations(
                        [
                            {
                                locale: "en",
                                value: fakerEN.word.words(),
                            },
                            {
                                locale: "ru",
                                value: fakerRU.word.words(),
                            },
                        ],
                        "name",
                    ),
                },
                description: {
                    create: toPrismaLocalizations(
                        [
                            {
                                locale: "en",
                                value: fakerEN.lorem.words(50),
                            },
                            {
                                locale: "ru",
                                value: fakerRU.lorem.words(50),
                            },
                        ],
                        "description",
                    ),
                },
            },
        });

        const post = await this.prisma.reactorPost.create({
            data: {
                authorId: mockId(1),
                hubId: hub.id,
                communityId: community.id,
                title: {
                    create: toPrismaLocalizations(
                        [
                            {
                                locale: "en",
                                value: fakerEN.word.words(),
                            },
                            {
                                locale: "ru",
                                value: fakerRU.word.words(),
                            },
                        ],
                        "title",
                    ),
                },
                body: {
                    create: toPrismaLocalizations(
                        [
                            {
                                locale: "en",
                                value: fakerEN.lorem.words(50),
                            },
                            {
                                locale: "ru",
                                value: fakerRU.lorem.words(50),
                            },
                        ],
                        "body",
                    ),
                },
                tags: {
                    connect: tags,
                },
            },
        });

        await this.prisma.reactorRating.createMany({
            data: randomRange(1, 10).map((_, i) => ({
                userId: mockId(i),
                entityType: "post",
                entityId: post.id,
                type: "like",
            })),
        });

        await this.prisma.reactorRating.createMany({
            data: randomRange(1, 10).map((_, i) => ({
                userId: mockId(10 + i),
                entityType: "post",
                entityId: post.id,
                type: "dislike",
            })),
        });

        await this.prisma.reactorUsefulness.createMany({
            data: randomRange(1, 10).map((_, i) => ({
                userId: mockId(i),
                entityType: "post",
                entityId: post.id,
                value: faker.number.int({ min: 1, max: 10 }),
            })),
        });

        return post;
    }
}
